package steps.filter_steps;

import actions.FilterActions;
import actions.SortActions;
import io.cucumber.java.en.And;
import io.cucumber.java.en.Then;
import io.cucumber.java.en.When;

public class FilterSteps {

    private FilterActions filterActions;
    private SortActions sortActions;

    @When("user applies price filter from {string} to {string}")
    public void userAppliesPriceFilterFromTo(String fromPrice, String toPrice) {
        filterActions.applyPriceFilter(fromPrice, toPrice);
    }

    @When("user clears price filter using clear filter button")
    public void userClearsPriceFilterUsingClearFilterButton() {
        filterActions.clearPriceFilterUsingClearButton();
    }

    @When("user clears all filters using clear all filters button")
    public void userClearsAllFiltersUsingClearAllFiltersButton() {
        filterActions.clearAllFilters();
    }

    @Then("verify price filter is applied in URL with {string}")
    public void verifyPriceFilterIsAppliedInURLWith(String expectedUrlParam) {
        filterActions.verifyPriceFilterInUrl(expectedUrlParam);
    }

    @Then("verify price filter is removed from URL")
    public void verifyPriceFilterIsRemovedFromURL() {
        filterActions.verifyPriceFilterRemovedFromUrl();
    }

    @And("verify all displayed products are within price range {string} to {string}")
    public void verifyAllDisplayedProductsAreWithinPriceRangeTo(String fromPrice, String toPrice) {
        filterActions.verifyProductsWithinPriceRange(fromPrice, toPrice);
    }

    @And("verify all displayed products have price greater than or equal to {string}")
    public void verifyAllDisplayedProductsHavePriceGreaterThanOrEqualTo(String minPrice) {
        filterActions.verifyProductsWithMinPrice(minPrice);
    }

    @And("verify all displayed products have price less than or equal to {string}")
    public void verifyAllDisplayedProductsHavePriceLessThanOrEqualTo(String maxPrice) {
        filterActions.verifyProductsWithMaxPrice(maxPrice);
    }

    @And("verify all displayed products have price equal to {string} or within reasonable range")
    public void verifyAllDisplayedProductsHavePriceEqualToOrWithinReasonableRange(String exactPrice) {
        filterActions.verifyProductsWithExactPrice(exactPrice);
    }

    @And("verify discounted products are included in price filter results")
    public void verifyDiscountedProductsAreIncludedInPriceFilterResults() {
        filterActions.verifyDiscountedProductsIncluded();
    }

    @And("verify all products are displayed without price filtering")
    public void verifyAllProductsAreDisplayedWithoutPriceFiltering() {
        filterActions.verifyAllProductsDisplayed();
    }

    // Integration with existing sort functionality
    @When("user applies price low to high sort")
    public void userAppliesPriceLowToHighSort() {
        sortActions.applySortOption("Cena: od najniższej");
    }

    @Then("verify price low to high sort is applied")
    public void verifyPriceLowToHighSortIsApplied() {
        sortActions.verifyAppliedSort("Cena: od najniższej");
    }

    @And("verify products are sorted by price in ascending order")
    public void verifyProductsAreSortedByPriceInAscendingOrder() {
        sortActions.verifyPriceSortingAscending();
    }
}
