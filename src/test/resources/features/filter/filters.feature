@regression
Feature: Category Page Price Filtering Tests

  Background: Navigate to category page with all products
    Given that user is on the home page
    And navigate to "Pokaż wszystko" subcategory from "Meble ogrodowe"
    And user loads all available products

  @filter @0201
  Scenario: Verify user can apply normal price range filter
    When user applies price filter from "500" to "2000"
    Then verify price filter is applied in URL with "500-2000"
    And verify all displayed products are within price range "500" to "2000"
    And verify discounted products are included in price filter results

  @filter @0202
  Scenario: Verify user can apply price filter with only from price
    When user applies price filter from "1000" to ""
    Then verify price filter is applied in URL with "1000-"
    And verify all displayed products have price greater than or equal to "1000"
    And verify discounted products are included in price filter results

  @filter @0203
  Scenario: Verify user can apply price filter with only to price
    When user applies price filter from "" to "1500"
    Then verify price filter is applied in URL with "-1500"
    And verify all displayed products have price less than or equal to "1500"
    And verify discounted products are included in price filter results

  @filter @0204
  Scenario: Verify user can apply price filter starting from 0 with to price
    When user applies price filter from "0" to "800"
    Then verify price filter is applied in URL with "0-800"
    And verify all displayed products have price less than or equal to "800"
    And verify discounted products are included in price filter results

  @filter @0205
  Scenario: Verify user can apply price filter starting from 0 without to price
    When user applies price filter from "0" to ""
    Then verify price filter is applied in URL with "0-"
    And verify all displayed products have price greater than or equal to "0"

  @filter @0206
  Scenario: Verify user can clear price filter using clear filter button
    When user applies price filter from "500" to "1500"
    Then verify price filter is applied in URL with "500-1500"
    When user clears price filter using clear filter button
    Then verify price filter is removed from URL
    And verify all products are displayed without price filtering

  @filter @0207
  Scenario: Verify user can clear price filter using clear all filters button
    When user applies price filter from "300" to "1200"
    Then verify price filter is applied in URL with "300-1200"
    When user clears all filters using clear all filters button
    Then verify price filter is removed from URL
    And verify all products are displayed without price filtering

  @filter @0208
  Scenario: Verify price filter persists when page is refreshed
    When user applies price filter from "600" to "1800"
    Then verify price filter is applied in URL with "600-1800"
    And verify all displayed products are within price range "600" to "1800"
    When user refreshes the page
    Then verify price filter is applied in URL with "600-1800"
    And verify all displayed products are within price range "600" to "1800"

  @filter @0209
  Scenario: Verify price filter persists after opening product and navigating back
    When user applies price filter from "400" to "1000"
    Then verify price filter is applied in URL with "400-1000"
    And verify all displayed products are within price range "400" to "1000"
    And user opens the first product from category page
    When user navigates back to the previous page
    Then verify price filter is applied in URL with "400-1000"
    And verify all displayed products are within price range "400" to "1000"

  @filter @0210
  Scenario: Verify price filter persists after adding product to cart and returning
    When user applies price filter from "700" to "1600"
    Then verify price filter is applied in URL with "700-1600"
    And verify all displayed products are within price range "700" to "1600"
    And user opens the first product from category page
    And user adds the product to cart
    And open cart and proceed to checkout
    When user navigates back to the previous page
    And user navigates back to the previous page
    And user navigates back to the previous page
    Then verify price filter is applied in URL with "700-1600"
    And verify all displayed products are within price range "700" to "1600"

  @filter @0211
  Scenario: Verify price filter works with high price range
    When user applies price filter from "2000" to "5000"
    Then verify price filter is applied in URL with "2000-5000"
    And verify all displayed products are within price range "2000" to "5000"
    And verify discounted products are included in price filter results

  @filter @0212
  Scenario: Verify price filter works with very low price range
    When user applies price filter from "50" to "200"
    Then verify price filter is applied in URL with "50-200"
    And verify all displayed products are within price range "50" to "200"
    And verify discounted products are included in price filter results

  @filter @0213
  Scenario: Verify price filter can be applied multiple times with different ranges
    When user applies price filter from "500" to "1000"
    Then verify price filter is applied in URL with "500-1000"
    When user applies price filter from "800" to "1500"
    Then verify price filter is applied in URL with "800-1500"
    And verify all displayed products are within price range "800" to "1500"

  @filter @0214
  Scenario: Verify price filter works with single price value (from and to same)
    When user applies price filter from "999" to "999"
    Then verify price filter is applied in URL with "999-999"
    And verify all displayed products have price equal to "999" or within reasonable range

  @filter @0215
  Scenario: Verify price filter can handle edge case with very specific range
    When user applies price filter from "999" to "1001"
    Then verify price filter is applied in URL with "999-1001"
    And verify all displayed products are within price range "999" to "1001"
    And verify discounted products are included in price filter results
