package pages;

import net.serenitybdd.core.steps.UIInteractionSteps;
import objects_behaviors.implementation.InputField;
import objects_behaviors.implementation.WebElement;
import objects_behaviors.implementation.WebList;
import objects_behaviors.rules.IInputField;
import objects_behaviors.rules.IWebElement;
import objects_behaviors.rules.IWebList;
import org.openqa.selenium.By;

public class CategoryPage extends UIInteractionSteps {
    private final By
            categoryPageHeader = By.xpath("/html/body/div/main/h1"),
            categoryPageDescription = By.xpath("/html/body/div[4]/main/div[2]/div/p"),
            seeMoreProductsButton = By.xpath("//button[@type=\"button\"]//span[contains(text(), \"Więcej produktów\")]"),
            PRICE_RANGE_FILTER = By.xpath("//div[@data-sentry-component=\"FilterRange\"]//button/div//div//p[contains(text(), '<PERSON>na')]"),
            LENGTH_FILTER = By.xpath("//div[@data-sentry-component=\"FilterRange\"]//button/div//div//p[contains(text(), 'Długość')]"),
            ARMREST_HEIGHT_FILTER = By.xpath("//div[@data-sentry-component=\"FilterRange\"]//button/div//div//p[contains(text(), 'Wysokość podłokietnika (min)')]"),
            WIDTH_FILTER = By.xpath("//div[@data-sentry-component=\"FilterRange\"]//button/div//div//p[contains(text(), 'Szerokość')]"),
            HEIGHT_FILTER = By.xpath("//div[@data-sentry-component=\"FilterRange\"]//button/div//div//p[contains(text(), 'Wysokość')]"),
            FRAME_COLOR_FILTER = By.xpath("//div[@data-sentry-component=\"MultiSelect\"]//button/div//div//p[contains(text(), 'Kolor stelaża')]"),
            FILLING_FILTER = By.xpath("//div[@data-sentry-component=\"MultiSelect\"]//button/div//div//p[contains(text(), 'Wypełnienie')]"),
            FABRIC_COLOR_FILTER = By.xpath("//div[@data-sentry-component=\"MultiSelect\"]//button/div//div//p[contains(text(), 'Kolor tkaniny')]"),
            COLLAPSE_FILTERS_BUTTON = By.xpath("//span[contains(text(), 'Zwiń filtry')]"),
            SHOW_FILTERS_BUTTON = By.xpath("//span[contains(text(), 'Pokaż filtry')]"),
            RANGE_INPUT_FROM = By.xpath("//div[@class=\"mt-2  border-primary absolute top-[55px] z-10 border w-full\"]//input[@placeholder=\"Od\"]"),
            RANGE_INPUT_TO = By.xpath("//div[@class=\"mt-2  border-primary absolute top-[55px] z-10 border w-full\"]//input[@placeholder=\"Do\"]"),
            APPLY_FILTER_BUTTON = By.xpath("//div[@class=\"mt-2  border-primary absolute top-[55px] z-10 border w-full\"]//span[contains(text(), 'Zastosuj')]"),
            CLEAR_FILTER_BUTTON = By.xpath("//div[@class=\"mt-2  border-primary absolute top-[55px] z-10 border w-full\"]//div[@data-sentry-component=\"IconButton\"]//button"),
            CLEAR_ALL_FILTERS_BUTTON = By.xpath("//span[contains(text(), 'Wyczyść wszystkie filtry')]"),
            FILTERS_LIST = By.xpath("//div[@data-sentry-component=\"MultiSelect\"]//div[@class=\"mt-2  border-primary absolute top-[55px] z-10 border w-full\"]//ul//li"),
            APPLIED_PRICE_FILTER_HEADER = By.xpath("//div[@data-sentry-component=\"FilterRange\"]//button/div//div//p[contains(text(), 'Cena')]//span[2]");

    public IWebElement categoryPageHeader() {
        return new WebElement($(categoryPageHeader), "Category Page Header");
    }

    public IWebElement categoryPageDescription() {
        return new WebElement($(categoryPageDescription), "Category Page Description");
    }

    public IWebElement seeMoreProductsButton() {
        return new WebElement($(seeMoreProductsButton), "See More Products Button");
    }

    public IWebElement priceRangeFilter() {
        return new WebElement($(PRICE_RANGE_FILTER), "Price Range Filter");
    }

    public IWebElement lengthFilter() {
        return new WebElement($(LENGTH_FILTER), "Length Filter");
    }

    public IWebElement armrestHeightFilter() {
        return new WebElement($(ARMREST_HEIGHT_FILTER), "Armrest Height Filter");
    }

    public IWebElement widthFilter() {
        return new WebElement($(WIDTH_FILTER), "Width Filter");
    }

    public IWebElement heightFilter() {
        return new WebElement($(HEIGHT_FILTER), "Height Filter");
    }

    public IWebElement frameColorFilter() {
        return new WebElement($(FRAME_COLOR_FILTER), "Frame Color Filter");
    }

    public IWebElement fillingFilter() {
        return new WebElement($(FILLING_FILTER), "Filling Filter");
    }

    public IWebElement fabricColorFilter() {
        return new WebElement($(FABRIC_COLOR_FILTER), "Fabric Color Filter");
    }

    public IWebElement collapseFiltersButton() {
        return new WebElement($(COLLAPSE_FILTERS_BUTTON), "Collapse Filters Button");
    }

    public IWebElement showFiltersButton() {
        return new WebElement($(SHOW_FILTERS_BUTTON), "Show Filters Button");
    }

    public IInputField rangeInputFrom() {
        return new InputField($(RANGE_INPUT_FROM), "Range Input From");
    }

    public IInputField rangeInputTo() {
        return new InputField($(RANGE_INPUT_TO), "Range Input To");
    }

    public IWebElement applyFilterButton() {
        return new WebElement($(APPLY_FILTER_BUTTON), "Apply Filter Button");
    }

    public IWebElement clearFilterButton() {
        return new WebElement($(CLEAR_FILTER_BUTTON), "Clear Filter Button");
    }

    public IWebElement clearAllFiltersButton() {
        return new WebElement($(CLEAR_ALL_FILTERS_BUTTON), "Clear All Filters Button");
    }

    public IWebList filtersList() {
        return new WebList($$(FILTERS_LIST), "Filters List");
    }
    public IWebElement appliedPriceFilterHeader() {
        return new WebElement($(APPLIED_PRICE_FILTER_HEADER), "Applied Price Filter Header");
    }
}
