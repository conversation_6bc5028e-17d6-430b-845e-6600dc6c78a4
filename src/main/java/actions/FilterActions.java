package actions;

import net.serenitybdd.core.Serenity;
import net.serenitybdd.core.steps.UIInteractionSteps;
import org.assertj.core.api.SoftAssertions;
import pages.CategoryPage;
import pages.SearchPage;

import java.time.Duration;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

public class FilterActions extends UIInteractionSteps {
    private CategoryPage categoryPage;
    private SearchPage searchPage;

    public void applyPriceFilter(String fromPrice, String toPrice) {
        // Click on price range filter to open the dropdown
        categoryPage.priceRangeFilter().waitUntilVisible(Duration.ofSeconds(10));
        categoryPage.priceRangeFilter().click();
        waitABit(1000);

        // Clear existing values and enter from price if provided
        if (fromPrice != null && !fromPrice.trim().isEmpty()) {
            categoryPage.rangeInputFrom().waitUntilVisible(Duration.ofSeconds(10));
            categoryPage.rangeInputFrom().clearText();
            categoryPage.rangeInputFrom().type(fromPrice);
        }

        // Clear existing values and enter to price if provided
        if (toPrice != null && !toPrice.trim().isEmpty()) {
            categoryPage.rangeInputTo().waitUntilVisible(Duration.ofSeconds(10));
            categoryPage.rangeInputTo().clearText();
            categoryPage.rangeInputTo().type(toPrice);
        }

        // Apply the filter
        categoryPage.applyFilterButton().waitUntilVisible(Duration.ofSeconds(10));
        categoryPage.applyFilterButton().click();
        
        // Wait for the filter to be applied and page to reload
        waitABit(3000);
    }

    public void clearPriceFilterUsingClearButton() {
        // Click on price range filter to open the dropdown
        categoryPage.priceRangeFilter().waitUntilVisible(Duration.ofSeconds(10));
        categoryPage.priceRangeFilter().click();
        waitABit(1000);

        // Click the clear filter button
        categoryPage.clearFilterButton().waitUntilVisible(Duration.ofSeconds(10));
        categoryPage.clearFilterButton().click();
        
        // Wait for the filter to be cleared and page to reload
        waitABit(3000);
    }

    public void clearAllFilters() {
        // Click the clear all filters button
        categoryPage.clearAllFiltersButton().waitUntilVisible(Duration.ofSeconds(10));
        categoryPage.clearAllFiltersButton().click();
        
        // Wait for all filters to be cleared and page to reload
        waitABit(3000);
    }

    public void verifyPriceFilterInUrl(String expectedUrlParam) {
        SoftAssertions softAssertions = new SoftAssertions();
        String currentUrl = Serenity.getDriver().getCurrentUrl();
        String expectedParam = "minimalPrice=" + expectedUrlParam;
        softAssertions.assertThat(currentUrl).contains(expectedParam);
        softAssertions.assertAll();
    }

    public void verifyPriceFilterRemovedFromUrl() {
        SoftAssertions softAssertions = new SoftAssertions();
        String currentUrl = Serenity.getDriver().getCurrentUrl();
        softAssertions.assertThat(currentUrl).doesNotContain("minimalPrice=");
        softAssertions.assertAll();
    }

    public void verifyProductsWithinPriceRange(String fromPrice, String toPrice) {
        SoftAssertions softAssertions = new SoftAssertions();
        waitABit(2000);
        
        List<Double> actualPrices = getProductPrices();
        
        // Filter out zero prices (failed parsing) for more accurate comparison
        List<Double> validActualPrices = actualPrices.stream()
                .filter(price -> price > 0.0)
                .collect(Collectors.toList());
        
        if (validActualPrices.isEmpty()) {
            System.out.println("No valid prices found to verify filtering");
            return;
        }

        double minPrice = fromPrice != null && !fromPrice.trim().isEmpty() ? Double.parseDouble(fromPrice) : 0.0;
        double maxPrice = toPrice != null && !toPrice.trim().isEmpty() ? Double.parseDouble(toPrice) : Double.MAX_VALUE;

        System.out.println("Verifying price range: " + minPrice + " - " + maxPrice);
        System.out.println("Found " + validActualPrices.size() + " valid prices");

        for (int i = 0; i < validActualPrices.size(); i++) {
            double price = validActualPrices.get(i);
            System.out.println("Product " + (i + 1) + " price: " + price);
            
            softAssertions.assertThat(price)
                    .as("Product " + (i + 1) + " price should be within range " + minPrice + " - " + maxPrice)
                    .isBetween(minPrice, maxPrice);
        }
        
        softAssertions.assertAll();
    }

    public void verifyProductsWithMinPrice(String minPrice) {
        SoftAssertions softAssertions = new SoftAssertions();
        waitABit(2000);
        
        List<Double> actualPrices = getProductPrices();
        
        // Filter out zero prices (failed parsing) for more accurate comparison
        List<Double> validActualPrices = actualPrices.stream()
                .filter(price -> price > 0.0)
                .collect(Collectors.toList());
        
        if (validActualPrices.isEmpty()) {
            System.out.println("No valid prices found to verify filtering");
            return;
        }

        double minimumPrice = Double.parseDouble(minPrice);

        System.out.println("Verifying minimum price: " + minimumPrice);
        System.out.println("Found " + validActualPrices.size() + " valid prices");

        for (int i = 0; i < validActualPrices.size(); i++) {
            double price = validActualPrices.get(i);
            System.out.println("Product " + (i + 1) + " price: " + price);
            
            softAssertions.assertThat(price)
                    .as("Product " + (i + 1) + " price should be >= " + minimumPrice)
                    .isGreaterThanOrEqualTo(minimumPrice);
        }
        
        softAssertions.assertAll();
    }

    public void verifyProductsWithMaxPrice(String maxPrice) {
        SoftAssertions softAssertions = new SoftAssertions();
        waitABit(2000);
        
        List<Double> actualPrices = getProductPrices();
        
        // Filter out zero prices (failed parsing) for more accurate comparison
        List<Double> validActualPrices = actualPrices.stream()
                .filter(price -> price > 0.0)
                .collect(Collectors.toList());
        
        if (validActualPrices.isEmpty()) {
            System.out.println("No valid prices found to verify filtering");
            return;
        }

        double maximumPrice = Double.parseDouble(maxPrice);

        System.out.println("Verifying maximum price: " + maximumPrice);
        System.out.println("Found " + validActualPrices.size() + " valid prices");

        for (int i = 0; i < validActualPrices.size(); i++) {
            double price = validActualPrices.get(i);
            System.out.println("Product " + (i + 1) + " price: " + price);
            
            softAssertions.assertThat(price)
                    .as("Product " + (i + 1) + " price should be <= " + maximumPrice)
                    .isLessThanOrEqualTo(maximumPrice);
        }
        
        softAssertions.assertAll();
    }

    public void verifyProductsWithExactPrice(String exactPrice) {
        SoftAssertions softAssertions = new SoftAssertions();
        waitABit(2000);
        
        List<Double> actualPrices = getProductPrices();
        
        // Filter out zero prices (failed parsing) for more accurate comparison
        List<Double> validActualPrices = actualPrices.stream()
                .filter(price -> price > 0.0)
                .collect(Collectors.toList());
        
        if (validActualPrices.isEmpty()) {
            System.out.println("No valid prices found to verify filtering");
            return;
        }

        double targetPrice = Double.parseDouble(exactPrice);
        double tolerance = 50.0; // Allow 50 PLN tolerance for "exact" price matching

        System.out.println("Verifying exact price: " + targetPrice + " (with tolerance: ±" + tolerance + ")");
        System.out.println("Found " + validActualPrices.size() + " valid prices");

        for (int i = 0; i < validActualPrices.size(); i++) {
            double price = validActualPrices.get(i);
            System.out.println("Product " + (i + 1) + " price: " + price);
            
            softAssertions.assertThat(price)
                    .as("Product " + (i + 1) + " price should be within tolerance of " + targetPrice)
                    .isBetween(targetPrice - tolerance, targetPrice + tolerance);
        }
        
        softAssertions.assertAll();
    }

    public void verifyDiscountedProductsIncluded() {
        SoftAssertions softAssertions = new SoftAssertions();
        waitABit(2000);
        
        // Check if there are any discounted products visible
        List<String> discountedPriceTexts = searchPage.discountedProductPriceOnSearchPage().getTexts();
        
        if (!discountedPriceTexts.isEmpty()) {
            System.out.println("Found " + discountedPriceTexts.size() + " discounted products");
            
            // Verify that discounted products are visible and their prices are parsed correctly
            for (int i = 0; i < discountedPriceTexts.size(); i++) {
                String discountedPriceText = discountedPriceTexts.get(i);
                if (discountedPriceText != null && !discountedPriceText.trim().isEmpty()) {
                    double discountedPrice = parsePriceFromText(discountedPriceText);
                    System.out.println("Discounted product " + (i + 1) + " price: " + discountedPrice);
                    
                    softAssertions.assertThat(discountedPrice)
                            .as("Discounted product " + (i + 1) + " should have valid price")
                            .isGreaterThan(0.0);
                }
            }
        } else {
            System.out.println("No discounted products found in current filter results");
        }
        
        softAssertions.assertAll();
    }

    public void verifyAllProductsDisplayed() {
        SoftAssertions softAssertions = new SoftAssertions();
        waitABit(2000);
        
        // Verify that products are displayed (no specific filtering applied)
        List<Double> actualPrices = getProductPrices();
        
        softAssertions.assertThat(actualPrices.size())
                .as("Products should be displayed after clearing filters")
                .isGreaterThan(0);
        
        System.out.println("Found " + actualPrices.size() + " products displayed after clearing filters");
        
        softAssertions.assertAll();
    }

    private List<Double> getProductPrices() {
        List<Double> prices = new ArrayList<>();
        
        // Wait for prices to be visible and give extra time for all products to load
        searchPage.productPriceOnSearchPage().waitUntilVisible(Duration.ofSeconds(10));
        waitABit(2000);
        
        // Get all regular prices first - refresh the elements to ensure we get all loaded products
        List<String> regularPriceTexts = searchPage.productPriceOnSearchPage().getTexts();
        List<String> discountedPriceTexts = searchPage.discountedProductPriceOnSearchPage().getTexts();
        
        // Also check how many product names we have to ensure consistency
        int productCount = searchPage.productName().getSize();
        
        // For each product, use discounted price if available, otherwise use regular price
        for (int i = 0; i < regularPriceTexts.size(); i++) {
            String priceToUse;

            // Check if there's a discounted price for this product
            if (i < discountedPriceTexts.size() &&
                    discountedPriceTexts.get(i) != null &&
                    !discountedPriceTexts.get(i).trim().isEmpty()) {
                priceToUse = discountedPriceTexts.get(i);
            } else {
                priceToUse = regularPriceTexts.get(i);
            }

            if (priceToUse != null && !priceToUse.trim().isEmpty()) {
                double parsedPrice = parsePriceFromText(priceToUse);
                prices.add(parsedPrice);
            }
        }
        
        // Warn if there's a significant mismatch between product count and price count
        if (Math.abs(productCount - prices.size()) > 5) {
            System.out.println("WARNING: Mismatch between product count (" + productCount + ") and price count (" + prices.size() + ")");
        }

        return prices;
    }

    private Double parsePriceFromText(String priceText) {
        if (priceText == null || priceText.trim().isEmpty()) {
            return 0.0;
        }

        // Handle price ranges (e.g., "59,00 zł – 69,90 zł")
        // For filtering purposes, we'll use the lower price (first price in the range)
        if (priceText.contains("–") || priceText.contains("-")) {
            String[] priceParts = priceText.split("[–-]");
            if (priceParts.length >= 1) {
                // Use the first (lower) price for filtering
                priceText = priceParts[0].trim();
            }
        }

        // Remove currency symbols and spaces
        String cleanPrice = priceText.toLowerCase()
                .replace("zł", "")
                .replace("pln", "")
                .replace(" ", "")
                .trim();

        // Handle Polish decimal format (comma as decimal separator)
        if (cleanPrice.contains(",")) {
            // Replace comma with dot, but only the last comma (decimal separator)
            int lastCommaIndex = cleanPrice.lastIndexOf(",");
            if (lastCommaIndex != -1) {
                cleanPrice = cleanPrice.substring(0, lastCommaIndex) + "." + cleanPrice.substring(lastCommaIndex + 1);
            }
        }

        // Extract only the numeric part (including decimal point)
        cleanPrice = cleanPrice.replaceAll("[^0-9.]", "");

        // Handle multiple dots (keep only the last one as decimal separator)
        if (cleanPrice.contains(".")) {
            int lastDotIndex = cleanPrice.lastIndexOf(".");
            String integerPart = cleanPrice.substring(0, lastDotIndex).replace(".", "");
            String decimalPart = cleanPrice.substring(lastDotIndex);
            cleanPrice = integerPart + decimalPart;
        }

        try {
            return Double.parseDouble(cleanPrice);
        } catch (NumberFormatException e) {
            // If parsing fails, return 0.0 as fallback
            return 0.0;
        }
    }
}
